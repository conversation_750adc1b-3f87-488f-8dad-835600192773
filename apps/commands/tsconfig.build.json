{"extends": "./tsconfig.json", "exclude": ["node_modules", "test", "dist", "**/*spec.ts"], "compilerOptions": {"module": "CommonJS", "resolveJsonModule": true, "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "outDir": "./dist"}}