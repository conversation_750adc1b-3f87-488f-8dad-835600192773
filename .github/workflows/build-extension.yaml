name: Build Extension

on:
  workflow_dispatch:

jobs:
  submit:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Zip extensions
        run: FRONTEND_URL=https://platform.postiz.com pnpm run build:extension

      - name: Upload to Nextcloud
        env:
          NEXTCLOUD_URL: ${{ secrets.NEXTCLOUD_URL }}
          NEXTCLOUD_USERNAME: ${{ secrets.NEXTCLOUD_USERNAME }}
          NEXTCLOUD_PASSWORD: ${{ secrets.NEXTCLOUD_PASSWORD }}
        run: |
          curl -T apps/extension/extension.zip \
            -u "$NEXTCLOUD_USERNAME:$NEXTCLOUD_PASSWORD" \
            "$NEXTCLOUD_URL/extension.zip"
