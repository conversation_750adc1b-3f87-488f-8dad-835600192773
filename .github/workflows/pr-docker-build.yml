name: Build and Publish PR Docker Image

on:
  pull_request_target:
    types: [opened, synchronize]

permissions: write-all

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ github.token }}

      - name: Set image tag
        id: vars
        run: echo "IMAGE_TAG=ghcr.io/gitroomhq/postiz-app-pr:${{ github.event.pull_request.number }}" >> $GITHUB_ENV

      - name: Build Docker image from Dockerfile.dev
        run: docker build -f Dockerfile.dev -t $IMAGE_TAG .

      - name: Push Docker image to GHCR
        run: docker push $IMAGE_TAG
